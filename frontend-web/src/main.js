/**
 * Smart Factory WMS Frontend - Main Application Entry Point
 *
 * This file initializes the Vue.js application with all necessary plugins,
 * configurations, and global components.
 */

import { createPinia } from "pinia";
import { Quasar } from "quasar";
import { createApp } from "vue";
import App from "./App.vue";
import i18n from "./plugins/i18n";
import toast from "./plugins/toast";
import router from "./router";

// Import Quasar css
import "quasar/src/css/index.sass";

// Import TailwindCSS
import "./css/app.css";

// Import Quasar and Pinia

// Global styles
import "./assets/styles/main.scss";

// Create Vue application instance
const app = createApp(App);
const pinia = createPinia();

// Configure global properties
app.config.globalProperties.$appName = "Smart Factory WMS";
app.config.globalProperties.$version = "1.0.0";

// Error handler for production
if (import.meta.env.PROD) {
  app.config.errorHandler = (error, instance, info) => {
    console.error("Global error:", error);
    console.error("Component instance:", instance);
    console.error("Error info:", info);

    // Send error to monitoring service
    // TODO: Implement error reporting service
  };
}

// Install plugins
app.use(pinia);
app.use(router);
app.use(Quasar, {
  plugins: {
    // Import Quasar plugins here
  },
});
app.use(i18n);
app.use(toast);

// Global components registration
import BaseButton from "./components/common/BaseButton.vue";
import BaseCard from "./components/common/BaseCard.vue";
import BaseDataTable from "./components/common/BaseDataTable.vue";
import BaseDialog from "./components/common/BaseDialog.vue";
import LoadingSpinner from "./components/common/LoadingSpinner.vue";

app.component("BaseCard", BaseCard);
app.component("BaseButton", BaseButton);
app.component("BaseDialog", BaseDialog);
app.component("BaseDataTable", BaseDataTable);
app.component("LoadingSpinner", LoadingSpinner);

// Mount application
app.mount("#app");

// Development tools
if (import.meta.env.DEV) {
  window.app = app;
  console.log("Smart Factory WMS Frontend loaded in development mode");
  console.log("Vue app instance available as window.app");
}
